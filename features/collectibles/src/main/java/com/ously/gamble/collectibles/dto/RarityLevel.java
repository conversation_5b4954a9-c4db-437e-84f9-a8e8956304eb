package com.ously.gamble.collectibles.dto;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum RarityLevel {
    COMMON(1),
    UNCOMMON(2),
    RARE(3);

    private final byte numericValue;

    RarityLevel(int numericValue) {
        this.numericValue = (byte) numericValue;
    }

    public byte getNumericValue() {
        return numericValue;
    }

    @JsonValue
    public String toJsonValue() {
        return name().toLowerCase();
    }

    @JsonCreator
    public static RarityLevel fromString(String value) {
        if (value == null) {
            return null;
        }
        
        try {
            return RarityLevel.valueOf(value.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid rarity level: " + value + 
                ". Valid values are: common, uncommon, rare (case insensitive)");
        }
    }

    public static RarityLevel fromNumericValue(byte numericValue) {
        for (RarityLevel rarity : values()) {
            if (rarity.numericValue == numericValue) {
                return rarity;
            }
        }
        throw new IllegalArgumentException("Invalid numeric rarity level: " + numericValue);
    }

    public static RarityLevel fromNumericValue(int numericValue) {
        return fromNumericValue((byte) numericValue);
    }
}
