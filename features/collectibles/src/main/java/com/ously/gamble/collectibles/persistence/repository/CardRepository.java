package com.ously.gamble.collectibles.persistence.repository;

import com.ously.gamble.collectibles.persistence.model.Card;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;


import java.util.List;
import java.util.Optional;

@Repository
public interface CardRepository extends JpaRepository<Card, Integer> {
    @EntityGraph(attributePaths = {"cardPieces"})
    List<Card> findByCardCollectionId(Integer cardCollectionId);

    Page<Card> findByCardCollectionId(Integer cardCollectionId, Pageable pageable);
    Optional<Card> findByIdAndCardCollectionId(Integer id, Integer cardCollectionId);

    boolean existsByNameAndCardCollectionId(String name, Integer cardCollectionId);
    boolean existsByNameAndCardCollectionIdAndIdNot(String name, Integer cardCollectionId, Integer id);

    @EntityGraph(attributePaths = {"cardPieces"})
    Optional<Card> findByIdWithCardPieces(Integer id);

    @EntityGraph(attributePaths = {"cardPieces"})
    Optional<Card> findByIdAndCardCollectionIdWithCardPieces(Integer id, Integer collectionId);

    @EntityGraph(attributePaths = {"cardPieces"})
    List<Card> findByCardCollectionIdOrderBySortOrderAscCreatedAtAsc(Integer cardCollectionId);
}
