package com.ously.gamble.collectibles.service;

import com.ously.gamble.collectibles.dto.CardDto;
import com.ously.gamble.collectibles.exception.CollectiblesLogicException;
import com.ously.gamble.collectibles.persistence.model.Card;
import com.ously.gamble.collectibles.persistence.model.CardCollection;
import com.ously.gamble.collectibles.persistence.model.CardPiece;
import com.ously.gamble.collectibles.persistence.repository.CardRepository;
import com.ously.gamble.collectibles.persistence.repository.CardCollectionRepository;
import com.ously.gamble.conditions.ConditionalOnBackend;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@ConditionalOnProperty(prefix = "collectibles", name = "enabled", havingValue = "true")
@ConditionalOnBackend
@Transactional
public class CardService {

    private final CardRepository cardRepository;
    private final CardCollectionRepository cardCollectionRepository;

    public CardService(CardRepository cardRepository, CardCollectionRepository cardCollectionRepository) {
        this.cardRepository = cardRepository;
        this.cardCollectionRepository = cardCollectionRepository;
    }

    @Transactional(readOnly = true)
    public Optional<Card> findByIdWithPieces(Integer cardId) {
        return cardRepository.findByIdWithCardPieces(cardId);
    }

    @Transactional(readOnly = true)
    public Optional<Card> findByIdAndCollectionId(Integer cardId, Integer collectionId) {
        return cardRepository.findByIdAndCardCollectionIdWithCardPieces(cardId, collectionId);
    }

    @Transactional(readOnly = true)
    public List<Card> findByCollectionId(Integer collectionId) {
        return cardRepository.findByCardCollectionIdOrderBySortOrderAscCreatedAtAsc(collectionId);
    }

    @Transactional(readOnly = true)
    public Page<Card> findByCollectionIdWithPieces(Integer collectionId, Pageable pageable) {
        return cardRepository.findByCardCollectionId(collectionId, pageable);
    }

    public Card createCard(Integer collectionId, CardDto.CreateCardRequest request) throws CollectiblesLogicException {
        CardCollection collection = cardCollectionRepository.findById(collectionId)
                .orElseThrow(() -> new CollectiblesLogicException("Collection not found with id: " + collectionId));

        if (cardRepository.existsByNameAndCardCollectionId(request.name(), collectionId)) {
            throw new CollectiblesLogicException("Card with name '" + request.name() + "' already exists in this collection");
        }

        // Validate pieces
        validateCardPieces(request.cardPieces());

        // Create card
        Card card = new Card(request.name(), request.imageUrl(), collection);
        card.setRarityLevel(request.rarityLevel());

        if (request.sortOrder() != null) {
            card.setSortOrder(request.sortOrder());
        }

        // Create card pieces
        List<CardPiece> cardPieces = new ArrayList<>();
        for (CardDto.CreateCardPieceRequest pieceRequest : request.cardPieces()) {
            CardPiece piece = new CardPiece(pieceRequest.pieceNumber(), pieceRequest.rarityLevel());
            cardPieces.add(piece);
        }

        card.updateCardPieces(cardPieces);
        
        return cardRepository.save(card);
    }

    public Optional<Card> updateCard(Integer collectionId, Integer cardId, CardDto.UpdateCardRequest request) throws CollectiblesLogicException {
        // Find card
        Optional<Card> cardOpt = cardRepository.findByIdAndCardCollectionId(cardId, collectionId);
        if (cardOpt.isEmpty()) {
            return Optional.empty();
        }

        Card card = cardOpt.get();

        // Validate unique name if changed
        if (request.name() != null && !request.name().equals(card.getName())) {
            if (cardRepository.existsByNameAndCardCollectionIdAndIdNot(request.name(), collectionId, cardId)) {
                throw new CollectiblesLogicException("Card with name '" + request.name() + "' already exists in this collection");
            }
            card.setName(request.name());
        }

        // Update other fields
        if (request.imageUrl() != null) {
            card.setImageUrl(request.imageUrl());
        }
        if (request.status() != null) {
            card.setStatus(request.status());
        }
        if (request.sortOrder() != null) {
            card.setSortOrder(request.sortOrder());
        }

        // Update card pieces if provided
        if (request.cardPieces() != null) {
            validateCardPieces(request.cardPieces());
            
            List<CardPiece> newPieces = new ArrayList<>();
            for (CardDto.UpdateCardPieceRequest pieceRequest : request.cardPieces()) {
                CardPiece piece = new CardPiece(pieceRequest.pieceNumber(), pieceRequest.rarityLevel());
                newPieces.add(piece);
            }
            
            card.updateCardPieces(newPieces);
        }

        return Optional.of(cardRepository.save(card));
    }

    public boolean deleteCard(Integer collectionId, Integer cardId) {
        Optional<Card> cardOpt = cardRepository.findByIdAndCardCollectionId(cardId, collectionId);
        if (cardOpt.isEmpty()) {
            return false;
        }

        cardRepository.delete(cardOpt.get());
        return true;
    }

    private void validateCardPieces(List<? extends Object> pieces) throws CollectiblesLogicException {
        if (pieces == null || pieces.size() != 4) {
            throw new CollectiblesLogicException("Card must have exactly 4 pieces");
        }

        // Check for duplicate piece numbers
        List<Integer> pieceNumbers = new ArrayList<>();
        for (Object piece : pieces) {
            Integer pieceNumber;
            if (piece instanceof CardDto.CreateCardPieceRequest createPiece) {
                pieceNumber = createPiece.pieceNumber();
            } else if (piece instanceof CardDto.UpdateCardPieceRequest updatePiece) {
                pieceNumber = updatePiece.pieceNumber();
            } else {
                throw new CollectiblesLogicException("Invalid piece type");
            }

            if (pieceNumbers.contains(pieceNumber)) {
                throw new CollectiblesLogicException("Duplicate piece number: " + pieceNumber);
            }
            pieceNumbers.add(pieceNumber);
        }

        // Validate piece numbers are 1-4
        for (Integer pieceNumber : pieceNumbers) {
            if (pieceNumber < 1 || pieceNumber > 4) {
                throw new CollectiblesLogicException("Piece number must be between 1 and 4");
            }
        }
    }
}
