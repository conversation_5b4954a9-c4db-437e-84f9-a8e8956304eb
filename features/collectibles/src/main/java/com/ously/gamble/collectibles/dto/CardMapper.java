package com.ously.gamble.collectibles.dto;

import com.ously.gamble.collectibles.persistence.model.Card;
import com.ously.gamble.collectibles.persistence.model.CardPiece;
import com.ously.gamble.conditions.ConditionalOnBackend;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.stream.Collectors;

@Component
@ConditionalOnProperty(prefix = "collectibles", name = "enabled", havingValue = "true")
@ConditionalOnBackend
public class CardMapper {

    public CardDto.CardResponse toCardResponse(Card card) {
        return new CardDto.CardResponse(
                card.getId(),
                card.getName(),
                card.getImageUrl(),
                RarityLevel.fromNumericValue(card.getRarityLevel()),
                card.getStatus(),
                card.getSortOrder(),
                card.getCardPieces().stream()
                        .sorted(Comparator.comparingInt(CardPiece::getPieceNumber))
                        .map(this::toCardPieceResponse)
                        .collect(Collectors.toList())
        );
    }

    public CardDto.CardPieceResponse toCardPieceResponse(CardPiece cardPiece) {
        return new CardDto.CardPieceResponse(
                cardPiece.getPieceNumber(),
                cardPiece.getRarityLevel()
        );
    }
}
