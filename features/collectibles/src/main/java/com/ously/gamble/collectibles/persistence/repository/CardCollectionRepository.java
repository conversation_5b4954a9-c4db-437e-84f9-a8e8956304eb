package com.ously.gamble.collectibles.persistence.repository;

import com.ously.gamble.collectibles.persistence.model.CardCollection;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface CardCollectionRepository extends JpaRepository<CardCollection, Integer> {

    List<CardCollection> findByNameIgnoreCase(String name);

    @Query("""
        SELECT cc FROM CardCollection cc
        WHERE LOWER(cc.name) = LOWER(:name)
        AND cc.id <> :excludeId
    """)
    List<CardCollection> findByNameIgnoreCaseExcludingId(@Param("name") String name, @Param("excludeId") Integer excludeId);

    @EntityGraph(attributePaths = {"cards", "rewards"})
    Optional<CardCollection> findByIdWithCardsAndRewards(Integer id);

    @EntityGraph(attributePaths = {"cards", "rewards"})
    Page<CardCollection> findAllWithCardsAndRewards(Pageable pageable);

    @Query("""
        SELECT cc FROM CardCollection cc
        WHERE (:status IS NULL OR cc.status = :status)
        AND (:startDateFrom IS NULL OR cc.startDate >= :startDateFrom)
        AND (:startDateTo IS NULL OR cc.startDate <= :startDateTo)
        AND (:endDateFrom IS NULL OR cc.endDate >= :endDateFrom)
        AND (:endDateTo IS NULL OR cc.endDate <= :endDateTo)
        ORDER BY cc.sortOrder ASC, cc.createdAt ASC
    """)
    Page<CardCollection> findAllFiltered(
            @Param("status") CardCollection.CollectionStatus status,
            @Param("startDateFrom") OffsetDateTime startDateFrom,
            @Param("startDateTo") OffsetDateTime startDateTo,
            @Param("endDateFrom") OffsetDateTime endDateFrom,
            @Param("endDateTo") OffsetDateTime endDateTo,
            Pageable pageable);

    @EntityGraph(attributePaths = {"cards", "rewards"})
    @Query("""
        SELECT DISTINCT cc FROM CardCollection cc
        WHERE (:status IS NULL OR cc.status = :status)
        AND (:startDateFrom IS NULL OR cc.startDate >= :startDateFrom)
        AND (:startDateTo IS NULL OR cc.startDate <= :startDateTo)
        AND (:endDateFrom IS NULL OR cc.endDate >= :endDateFrom)
        AND (:endDateTo IS NULL OR cc.endDate <= :endDateTo)
        ORDER BY cc.sortOrder ASC, cc.createdAt ASC
    """)
    Page<CardCollection> findAllWithCardsAndRewardsFiltered(
            @Param("status") CardCollection.CollectionStatus status,
            @Param("startDateFrom") OffsetDateTime startDateFrom,
            @Param("startDateTo") OffsetDateTime startDateTo,
            @Param("endDateFrom") OffsetDateTime endDateFrom,
            @Param("endDateTo") OffsetDateTime endDateTo,
            Pageable pageable);


}
