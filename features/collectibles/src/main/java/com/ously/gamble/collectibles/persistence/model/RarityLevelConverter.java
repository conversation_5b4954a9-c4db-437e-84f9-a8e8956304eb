package com.ously.gamble.collectibles.persistence.model;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

@Converter(autoApply = false)
public class RarityLevelConverter implements AttributeConverter<Card.RarityLevel, Byte> {

    @Override
    public Byte convertToDatabaseColumn(Card.RarityLevel rarityLevel) {
        if (rarityLevel == null) {
            return null;
        }
        return rarityLevel.getNumericValue();
    }

    @Override
    public Card.RarityLevel convertToEntityAttribute(Byte dbValue) {
        if (dbValue == null) {
            return null;
        }
        return Card.RarityLevel.fromNumericValue(dbValue);
    }
}
