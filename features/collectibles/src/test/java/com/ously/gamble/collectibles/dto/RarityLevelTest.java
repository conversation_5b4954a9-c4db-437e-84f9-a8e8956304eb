package com.ously.gamble.collectibles.dto;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class RarityLevelTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    void testFromString_CaseInsensitive() {
        assertEquals(RarityLevel.COMMON, RarityLevel.fromString("common"));
        assertEquals(RarityLevel.COMMON, RarityLevel.fromString("COMMON"));
        assertEquals(RarityLevel.COMMON, RarityLevel.fromString("Common"));
        
        assertEquals(RarityLevel.UNCOMMON, RarityLevel.fromString("uncommon"));
        assertEquals(RarityLevel.UNCOMMON, RarityLevel.fromString("UNCOMMON"));
        assertEquals(RarityLevel.UNCOMMON, RarityLevel.fromString("Uncommon"));
        
        assertEquals(<PERSON>rityLevel.RARE, RarityLevel.fromString("rare"));
        assertEquals(RarityLevel.RARE, RarityLevel.fromString("RARE"));
        assertEquals(RarityLevel.RARE, RarityLevel.fromString("Rare"));
    }

    @Test
    void testFromString_Null() {
        assertNull(RarityLevel.fromString(null));
    }

    @Test
    void testFromString_Invalid() {
        assertThrows(IllegalArgumentException.class, () -> RarityLevel.fromString("invalid"));
        assertThrows(IllegalArgumentException.class, () -> RarityLevel.fromString("epic"));
    }

    @Test
    void testNumericValues() {
        assertEquals(1, RarityLevel.COMMON.getNumericValue());
        assertEquals(2, RarityLevel.UNCOMMON.getNumericValue());
        assertEquals(3, RarityLevel.RARE.getNumericValue());
    }

    @Test
    void testFromNumericValue() {
        assertEquals(RarityLevel.COMMON, RarityLevel.fromNumericValue(1));
        assertEquals(RarityLevel.UNCOMMON, RarityLevel.fromNumericValue(2));
        assertEquals(RarityLevel.RARE, RarityLevel.fromNumericValue(3));
    }

    @Test
    void testFromNumericValue_Invalid() {
        assertThrows(IllegalArgumentException.class, () -> RarityLevel.fromNumericValue(0));
        assertThrows(IllegalArgumentException.class, () -> RarityLevel.fromNumericValue(4));
    }

    @Test
    void testEntityRarityConversion() {
        assertEquals(RarityLevel.COMMON, RarityLevel.fromEntityRarity(com.ously.gamble.collectibles.persistence.model.Card.RarityLevel.COMMON));
        assertEquals(RarityLevel.UNCOMMON, RarityLevel.fromEntityRarity(com.ously.gamble.collectibles.persistence.model.Card.RarityLevel.UNCOMMON));
        assertEquals(RarityLevel.RARE, RarityLevel.fromEntityRarity(com.ously.gamble.collectibles.persistence.model.Card.RarityLevel.RARE));
    }

    @Test
    void testJsonSerialization() throws Exception {
        assertEquals("\"common\"", objectMapper.writeValueAsString(RarityLevel.COMMON));
        assertEquals("\"uncommon\"", objectMapper.writeValueAsString(RarityLevel.UNCOMMON));
        assertEquals("\"rare\"", objectMapper.writeValueAsString(RarityLevel.RARE));
    }

    @Test
    void testJsonDeserialization() throws Exception {
        assertEquals(RarityLevel.COMMON, objectMapper.readValue("\"common\"", RarityLevel.class));
        assertEquals(RarityLevel.COMMON, objectMapper.readValue("\"COMMON\"", RarityLevel.class));
        assertEquals(RarityLevel.UNCOMMON, objectMapper.readValue("\"uncommon\"", RarityLevel.class));
        assertEquals(RarityLevel.RARE, objectMapper.readValue("\"rare\"", RarityLevel.class));
    }
}
